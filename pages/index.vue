<script setup lang="ts">
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Progress } from '@/components/ui/progress'
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable'
import { ArrowLeftIcon, CheckIcon, EyeIcon } from 'lucide-vue-next'
import { computed, reactive, ref } from 'vue'

// 视频URL输入
const videoUrl = ref('')

// 分析状态
const analyzing = ref(false)

// 分析步骤状态
const steps = reactive([
  { id: 'validate', name: '验证地址', status: 'pending' },
  { id: 'parse', name: '解析视频', status: 'pending' },
  { id: 'analyze', name: '内容分析', status: 'pending' },
  { id: 'summarize', name: '生成总结', status: 'pending' },
])

// 分析结果
const result = ref<{
  title: string
  summary: string
  keyPoints: string[]
} | null>(null)

// 是否显示结果页面
const showResult = ref(false)

// 分析是否已完成
const analysisCompleted = ref(false)

// 计算是否显示"查看结果"按钮
const showViewResultButton = computed(() => {
  return analysisCompleted.value && !showResult.value && result.value
})

// 测试功能：一键显示分析过程和结果
function quickTest() {
  // 设置测试URL
  videoUrl.value = 'https://example.com/test-video.mp4'

  // 重置所有状态
  analyzing.value = false
  analysisCompleted.value = false
  showResult.value = false
  result.value = null

  // 重置步骤状态
  steps.forEach((step) => {
    step.status = 'pending'
  })

  // 立即设置所有步骤为完成状态
  setTimeout(() => {
    steps.forEach((step) => {
      step.status = 'complete'
    })

    // 设置测试结果
    result.value = {
      title: '测试视频：AI技术发展趋势分析',
      summary:
        '这是一个关于人工智能技术发展趋势的测试视频。视频详细介绍了当前AI技术的主要发展方向，包括机器学习、深度学习、自然语言处理等领域的最新进展。内容涵盖了技术原理、应用场景、发展前景以及可能面临的挑战。',
      keyPoints: [
        '机器学习算法在各行业的广泛应用',
        '深度学习技术在图像识别和语音处理方面的突破',
        '自然语言处理技术的快速发展和应用',
        'AI伦理和安全问题日益受到关注',
        '未来AI技术将更加注重可解释性和可控性',
      ],
    }

    analysisCompleted.value = true
    showResult.value = true
  }, 500)
}

// 开始分析
function startAnalyzing() {
  if (!videoUrl.value.trim()) {
    return
  }

  analyzing.value = true

  // 重置步骤状态
  steps.forEach((step) => {
    step.status = 'pending'
  })

  // 模拟分析过程
  simulateAnalysis()
}

// 模拟分析过程
async function simulateAnalysis() {
  // 模拟验证地址
  steps[0].status = 'processing'
  // 使用多个小步骤来更新进度条，让进度条看起来在动
  for (let i = 0; i < 5; i++) {
    await new Promise((resolve) => setTimeout(resolve, 300))
  }
  steps[0].status = 'complete'

  // 模拟解析视频
  steps[1].status = 'processing'
  for (let i = 0; i < 6; i++) {
    await new Promise((resolve) => setTimeout(resolve, 300))
  }
  steps[1].status = 'complete'

  // 模拟内容分析
  steps[2].status = 'processing'
  for (let i = 0; i < 8; i++) {
    await new Promise((resolve) => setTimeout(resolve, 300))
  }
  steps[2].status = 'complete'

  // 模拟生成总结
  steps[3].status = 'processing'
  for (let i = 0; i < 6; i++) {
    await new Promise((resolve) => setTimeout(resolve, 300))
  }
  steps[3].status = 'complete'

  // 设置模拟结果
  result.value = {
    title: '视频标题示例',
    summary: '这是一个视频内容总结的示例。这里将展示AI对视频内容的详细分析和总结，包括关键点、主要内容和重要信息等。',
    keyPoints: ['关键点1: 视频的主要主题', '关键点2: 视频中的重要信息', '关键点3: 视频的结论或观点'],
  }

  // 显示结果页面
  setTimeout(() => {
    analyzing.value = false
    showResult.value = true
    analysisCompleted.value = true
  }, 100)
}
</script>

<template>
  <div class="h-full flex flex-1 overflow-hidden">
    <ResizablePanelGroup direction="horizontal" class="h-full w-full">
      <!-- 左侧输入面板 -->
      <ResizablePanel :default-size="showResult ? 50 : 100" :min-size="30" class="transition-all duration-300 ease-out">
        <div class="h-full flex flex-col items-center justify-center px-6 py-10">
          <div class="max-w-2xl w-full">
            <div class="mb-4 flex justify-end">
              <Button variant="outline" size="sm" class="rounded-lg text-xs" @click="quickTest"> 🧪 快速测试 </Button>
            </div>
            <h1 class="mb-2 text-center text-4xl font-bold">视频内容智能总结</h1>
            <p class="mb-8 text-center text-xl text-muted-foreground">输入视频URL，获取AI生成的详细内容总结</p>

            <div class="w-full flex flex-col gap-4">
              <div class="flex gap-2">
                <Input
                  v-model="videoUrl"
                  placeholder="请输入视频URL"
                  class="h-12 flex-1 rounded-xl px-4 text-lg"
                  :disabled="analyzing"
                  @keyup.enter="startAnalyzing"
                />
                <Button :disabled="!videoUrl.trim() || analyzing" class="h-12 rounded-xl px-6" @click="startAnalyzing">
                  开始分析
                </Button>
              </div>

              <!-- 分析进度 -->
              <div v-if="analyzing || analysisCompleted" class="mt-4 w-full">
                <div class="space-y-3">
                  <div v-for="step in steps" :key="step.id" class="flex items-center gap-2">
                    <div
                      v-if="step.status === 'pending'"
                      class="h-5 w-5 border border-muted-foreground/30 rounded-full"
                    />
                    <Icon
                      v-else-if="step.status === 'processing'"
                      name="i-lucide-loader-2"
                      class="h-5 w-5 animate-spin text-primary"
                    />
                    <CheckIcon v-else class="h-5 w-5 text-primary" />
                    <span :class="{ 'text-muted-foreground': step.status === 'pending' }">{{ step.name }}</span>
                  </div>
                </div>

                <!-- 查看结果按钮 -->
                <div v-if="showViewResultButton" class="mt-4 flex justify-center">
                  <Button class="rounded-xl" @click="showResult = true">
                    <EyeIcon class="mr-2 h-4 w-4" />
                    查看分析结果
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ResizablePanel>

      <!-- 可拖拽分割线 -->
      <ResizableHandle
        v-show="showResult"
        class="bg-0 transition-all duration-300 ease-out"
        :class="{ 'opacity-0 pointer-events-none': !showResult }"
      />

      <!-- 右侧结果面板 -->
      <ResizablePanel
        :default-size="showResult ? 50 : 0"
        :min-size="showResult ? 30 : 0"
        :max-size="showResult ? 70 : 0"
        class="overflow-hidden rounded-xl transition-all duration-300 ease-out"
      >
        <div
          v-show="showResult"
          v-motion
          :initial="{ opacity: 0, x: 30, scale: 0.98 }"
          :enter="{
            opacity: 1,
            x: 0,
            scale: 1,
            transition: {
              duration: 250,
              ease: 'easeOut',
            },
          }"
          :leave="{
            opacity: 0,
            x: 100,
            scale: 0.95,
            transition: {
              duration: 250,
              ease: 'easeIn',
            },
          }"
          class="h-full flex flex-col border border-border/10 rounded-xl bg-muted/40 p-6 shadow-sm"
        >
          <div class="mb-6 flex items-center justify-between">
            <h2
              v-motion
              :initial="{ opacity: 0, y: -10 }"
              :enter="{ opacity: 1, y: 0, transition: { delay: 100, duration: 250 } }"
              class="text-2xl font-bold"
            >
              {{ result?.title }}
            </h2>
            <Button
              v-motion
              :initial="{ opacity: 0, scale: 0.9 }"
              :enter="{ opacity: 1, scale: 1, transition: { delay: 150, duration: 200 } }"
              variant="ghost"
              size="sm"
              class="rounded-xl"
              @click="showResult = false"
            >
              <ArrowLeftIcon class="mr-2 h-4 w-4" />
              隐藏结果
            </Button>
          </div>

          <div class="flex-1 overflow-y-auto space-y-6">
            <div
              v-motion
              :initial="{ opacity: 0, y: 15 }"
              :enter="{ opacity: 1, y: 0, transition: { delay: 200, duration: 300 } }"
            >
              <h3 class="mb-2 text-lg font-semibold">内容总结</h3>
              <p class="text-muted-foreground leading-relaxed">
                {{ result?.summary }}
              </p>
            </div>

            <div
              v-motion
              :initial="{ opacity: 0, y: 15 }"
              :enter="{ opacity: 1, y: 0, transition: { delay: 250, duration: 300 } }"
            >
              <h3 class="mb-2 text-lg font-semibold">关键点</h3>
              <ul class="list-disc list-inside space-y-2">
                <li
                  v-for="(point, index) in result?.keyPoints"
                  :key="index"
                  v-motion
                  :initial="{ opacity: 0, x: -10 }"
                  :enter="{ opacity: 1, x: 0, transition: { delay: 300 + index * 50, duration: 250 } }"
                  class="text-muted-foreground"
                >
                  {{ point }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </ResizablePanel>
    </ResizablePanelGroup>
  </div>
</template>
