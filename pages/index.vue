<script setup lang="ts">
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Progress } from '@/components/ui/progress'
import { ArrowLeftIcon, CheckIcon } from 'lucide-vue-next'
import { reactive, ref } from 'vue'

// 视频URL输入
const videoUrl = ref('')

// 分析状态
const analyzing = ref(false)

// 分析步骤状态
const steps = reactive([
  { id: 'validate', name: '验证地址', status: 'pending' },
  { id: 'parse', name: '解析视频', status: 'pending' },
  { id: 'analyze', name: '内容分析', status: 'pending' },
  { id: 'summarize', name: '生成总结', status: 'pending' },
])

// 分析结果
const result = ref(null)

// 是否显示结果页面
const showResult = ref(false)

// 开始分析
function startAnalyzing() {
  if (!videoUrl.value.trim()) {
    return
  }

  analyzing.value = true

  // 重置步骤状态
  steps.forEach((step) => {
    step.status = 'pending'
  })

  // 模拟分析过程
  simulateAnalysis()
}

// 模拟分析过程
async function simulateAnalysis() {
  // 模拟验证地址
  steps[0].status = 'processing'
  await new Promise((resolve) => setTimeout(resolve, 1500))
  steps[0].status = 'complete'

  // 模拟解析视频
  steps[1].status = 'processing'
  await new Promise((resolve) => setTimeout(resolve, 2000))
  steps[1].status = 'complete'

  // 模拟内容分析
  steps[2].status = 'processing'
  await new Promise((resolve) => setTimeout(resolve, 2500))
  steps[2].status = 'complete'

  // 模拟生成总结
  steps[3].status = 'processing'
  await new Promise((resolve) => setTimeout(resolve, 2000))
  steps[3].status = 'complete'

  // 设置模拟结果
  result.value = {
    title: '视频标题示例',
    summary: '这是一个视频内容总结的示例。这里将展示AI对视频内容的详细分析和总结，包括关键点、主要内容和重要信息等。',
    keyPoints: ['关键点1: 视频的主要主题', '关键点2: 视频中的重要信息', '关键点3: 视频的结论或观点'],
  }

  // 显示结果页面
  setTimeout(() => {
    analyzing.value = false
    showResult.value = true
  }, 1000)
}
</script>

<template>
  <div class="relative flex flex-1 flex-col items-center justify-center overflow-hidden px-4 py-10">
    <!-- 主页内容 -->
    <div
      class="max-w-3xl w-full flex flex-col items-center justify-center transition-transform duration-500 ease-in-out"
      :class="{ '-translate-x-full opacity-0': showResult }"
    >
      <h1 class="mb-2 text-center text-4xl font-bold">视频内容智能总结</h1>
      <p class="mb-8 text-center text-xl text-muted-foreground">输入视频URL，获取AI生成的详细内容总结</p>

      <div class="w-full flex flex-col gap-4">
        <div class="flex gap-2">
          <Input v-model="videoUrl" placeholder="请输入视频URL" class="flex-1" :disabled="analyzing" />
          <Button :disabled="!videoUrl.trim() || analyzing" @click="startAnalyzing"> 开始分析 </Button>
        </div>

        <!-- 分析进度 -->
        <div v-if="analyzing" class="mt-4 w-full">
          <div class="mb-4">
            <p class="mb-2 text-sm font-medium">分析进度</p>
            <Progress :value="(steps.filter((s) => s.status === 'complete').length / steps.length) * 100" />
          </div>

          <div class="space-y-3">
            <div v-for="step in steps" :key="step.id" class="flex items-center gap-2">
              <div v-if="step.status === 'pending'" class="h-5 w-5 border border-muted-foreground/30 rounded-full" />
              <Icon
                v-else-if="step.status === 'processing'"
                name="i-lucide-loader-2"
                class="h-5 w-5 animate-spin text-primary"
              />
              <CheckIcon v-else class="h-5 w-5 text-primary" />
              <span :class="{ 'text-muted-foreground': step.status === 'pending' }">{{ step.name }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 结果页面 -->
    <div
      class="absolute left-0 top-0 h-full w-full flex items-center justify-center transition-transform duration-500 ease-in-out"
      :class="{ 'translate-x-full opacity-0': !showResult }"
    >
      <div v-if="result" class="max-w-3xl w-full rounded-lg bg-card p-6 shadow-lg">
        <div class="mb-6 flex items-center justify-between">
          <h2 class="text-2xl font-bold">
            {{ result.title }}
          </h2>
          <Button variant="outline" size="sm" @click="showResult = false">
            <ArrowLeftIcon class="mr-2 h-4 w-4" />
            返回
          </Button>
        </div>

        <div class="space-y-6">
          <div>
            <h3 class="mb-2 text-lg font-semibold">内容总结</h3>
            <p class="text-muted-foreground">
              {{ result.summary }}
            </p>
          </div>

          <div>
            <h3 class="mb-2 text-lg font-semibold">关键点</h3>
            <ul class="list-disc list-inside space-y-1">
              <li v-for="(point, index) in result.keyPoints" :key="index" class="text-muted-foreground">
                {{ point }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
